import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { AppModule } from './app.module';
import * as helmet from 'helmet';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // Get environment variables
  const port = configService.get<number>('PORT', 4000);
  const frontendUrl = configService.get<string>('FRONTEND_URL', 'http://localhost:3000');
  const nodeEnv = configService.get<string>('NODE_ENV', 'development');

  // Enhanced security middleware with NIS2-compliant configuration
  app.use(helmet.default({
    // Content Security Policy - handled by our custom middleware
    contentSecurityPolicy: false, // We handle this in SecurityHeadersMiddleware

    // HSTS - handled by our custom middleware for better control
    hsts: false, // We handle this in SecurityHeadersMiddleware

    // Cross-origin policies
    crossOriginEmbedderPolicy: false,
    crossOriginOpenerPolicy: false,
    crossOriginResourcePolicy: false,

    // DNS prefetch control
    dnsPrefetchControl: { allow: false },

    // Frame options
    frameguard: { action: 'deny' },

    // Hide powered by header
    hidePoweredBy: true,

    // IE no open
    ieNoOpen: true,

    // No sniff
    noSniff: true,

    // Origin agent cluster
    originAgentCluster: true,

    // Permissions policy
    permittedCrossDomainPolicies: false,

    // Referrer policy
    referrerPolicy: { policy: 'strict-origin-when-cross-origin' },

    // XSS filter
    xssFilter: true,
  }));

  // Enhanced CORS configuration for NIS2 compliance
  app.enableCors({
    origin: (origin, callback) => {
      const allowedOrigins = [
        frontendUrl,
        'https://dev.trusthansen.dk',
        'http://localhost:3080'
      ];

      // Allow requests with no origin (mobile apps, Postman, etc.) in development
      if (!origin && nodeEnv === 'development') {
        return callback(null, true);
      }

      if (allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        callback(new Error('Not allowed by CORS policy'), false);
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
      'X-CSRF-Token',
      'X-Session-ID',
      'X-Request-ID'
    ],
    exposedHeaders: [
      'X-Total-Count',
      'X-Page-Count',
      'X-Per-Page',
      'X-Request-ID',
      'X-Security-Timestamp'
    ],
    maxAge: 86400, // 24 hours
    optionsSuccessStatus: 204,
  });

  // 🔐 SECURITY: Enhanced global validation pipe for comprehensive input validation
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true, // Strip properties that don't have decorators
      forbidNonWhitelisted: true, // Throw error if non-whitelisted properties are present
      transform: true, // Automatically transform payloads to DTO instances
      disableErrorMessages: nodeEnv === 'production', // Hide detailed validation errors in production
      validateCustomDecorators: true, // Enable custom validation decorators
      forbidUnknownValues: true, // Forbid unknown objects
      stopAtFirstError: false, // Validate all properties, not just the first error
      dismissDefaultMessages: false, // Keep default validation messages
      validationError: {
        target: false, // Don't expose the target object in validation errors
        value: false, // Don't expose the value in validation errors
      },
    }),
  );

  // Global API prefix
  app.setGlobalPrefix('api');

  // 🔐 NIS2-Compliant Security Configuration
  if (nodeEnv === 'production') {
    // Trust proxy for HTTPS enforcement
    (app as any).set('trust proxy', 1);

    // Enforce HTTPS in production
    app.use((req: any, res: any, next: any) => {
      if (req.header('x-forwarded-proto') !== 'https') {
        res.redirect(`https://${req.header('host')}${req.url}`);
      } else {
        next();
      }
    });
  }

  // Security logging for production
  if (nodeEnv === 'production') {
    console.log('🔐 [SECURITY] Production security measures enabled:', {
      httpsEnforcement: true,
      securityHeaders: true,
      corsRestricted: true,
      rateLimiting: true,
      auditLogging: true,
      timestamp: new Date().toISOString()
    });
  }

  // Configure Swagger for API documentation
  if (nodeEnv !== 'production') {
    const swaggerConfig = new DocumentBuilder()
      .setTitle('Employee Performance Dashboard API')
      .setDescription('API documentation for the Employee Performance Management Dashboard')
      .setVersion('1.0')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          name: 'Authorization',
          in: 'header',
        },
        'JWT',
      )
      .build();

    const document = SwaggerModule.createDocument(app, swaggerConfig);
    SwaggerModule.setup('api/docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
      },
    });
  }

  await app.listen(port, '0.0.0.0');
  console.log(`Application is running on: http://0.0.0.0:${port}/api`);
  console.log(`Swagger documentation: http://0.0.0.0:${port}/api/docs`);
}
bootstrap();
