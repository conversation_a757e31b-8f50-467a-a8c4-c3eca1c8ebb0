import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  IconButton,
  Menu,
  MenuItem,
  Chip,
  Alert,
  CircularProgress,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Share as ShareIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { ApiService } from '../../services/api';
import { AnalyticsDashboard as DashboardType } from '../../types';
// Removed legacy api-old import - using centralized API service
// 🔐 NIS2-COMPLIANT: Enhanced analytics widgets with real data
import EngagementTrendsWidget from './widgets/EngagementTrendsWidget';
import AttritionRiskWidget from './widgets/AttritionRiskWidget';
import EnhancedPerformanceWidget from './widgets/EnhancedPerformanceWidget';
import TeamComparisonWidget from './widgets/TeamComparisonWidget';
import PerformanceOverviewWidget from './widgets/PerformanceOverviewWidget';
import RecognitionFeedWidget from './widgets/RecognitionFeedWidget';
import TeamPerformanceWidget from './widgets/TeamPerformanceWidget';
import ExportDialog from './ExportDialog';
import { isWidgetEnabled } from '../../utils/config';
import { logAnalyticsAccess, logUserAction } from '../../utils/audit';

interface AnalyticsDashboardProps {
  userId?: number;
}

const AnalyticsDashboard: React.FC<AnalyticsDashboardProps> = ({ userId: _userId }) => {
  const [dashboards, setDashboards] = useState<DashboardType[]>([]);
  const [selectedDashboard, setSelectedDashboard] = useState<DashboardType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [menuDashboard, setMenuDashboard] = useState<DashboardType | null>(null);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [newDashboardName, setNewDashboardName] = useState('');
  const [newDashboardDescription, setNewDashboardDescription] = useState('');
  const [exportDialogOpen, setExportDialogOpen] = useState(false);

  useEffect(() => {
    loadDashboards();
  }, []);

  const loadDashboards = async () => {
    try {
      setLoading(true);
      const data = await ApiService.getDashboards();
      setDashboards(data);

      // Select default dashboard or first one
      const defaultDashboard = data.find(d => d.isDefault) || data[0];
      if (defaultDashboard) {
        setSelectedDashboard(defaultDashboard);
      }
    } catch (err) {
      setError('Failed to load dashboards');
      console.error('Error loading dashboards:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, dashboard: DashboardType) => {
    setAnchorEl(event.currentTarget);
    setMenuDashboard(dashboard);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setMenuDashboard(null);
  };

  const handleCreateDashboard = async () => {
    try {
      const newDashboard = await ApiService.createDashboard({
        name: newDashboardName,
        description: newDashboardDescription,
        layoutConfig: { layout: 'grid', columns: 3, rows: 4 },
        widgetConfig: { widgets: [] },
        isDefault: dashboards.length === 0,
        isShared: false,
      });

      // 🔐 NIS2-COMPLIANT: Log dashboard creation for audit
      logUserAction('dashboard_created', 'analytics_dashboard', {
        dashboardId: newDashboard.id,
        dashboardName: newDashboardName,
        description: newDashboardDescription,
      });

      setDashboards([...dashboards, newDashboard]);
      setSelectedDashboard(newDashboard);
      setCreateDialogOpen(false);
      setNewDashboardName('');
      setNewDashboardDescription('');
    } catch (err) {
      setError('Failed to create dashboard');
      console.error('Error creating dashboard:', err);
    }
  };

  const handleDeleteDashboard = async (dashboard: DashboardType) => {
    try {
      await ApiService.deleteDashboard(dashboard.id.toString());

      // 🔐 NIS2-COMPLIANT: Log dashboard deletion for audit
      logUserAction('dashboard_deleted', 'analytics_dashboard', {
        dashboardId: dashboard.id,
        dashboardName: dashboard.name,
      });

      const updatedDashboards = dashboards.filter(d => d.id !== dashboard.id);
      setDashboards(updatedDashboards);

      if (selectedDashboard?.id === dashboard.id) {
        setSelectedDashboard(updatedDashboards[0] || null);
      }
      handleMenuClose();
    } catch (err) {
      setError('Failed to delete dashboard');
      console.error('Error deleting dashboard:', err);
    }
  };

  // 🔐 NIS2-COMPLIANT: Enhanced widget rendering with real data components
  const renderWidget = (widgetType: string, position: any) => {
    const commonProps = { key: `${widgetType}-${position.x}-${position.y}` };

    // Check if widget is enabled for this organization
    if (!isWidgetEnabled(widgetType)) {
      return (
        <Card {...commonProps} sx={{ height: 300 }}>
          <CardContent>
            <Box display="flex" alignItems="center" justifyContent="center" height="100%">
              <Typography color="textSecondary">
                Widget not available in current configuration
              </Typography>
            </Box>
          </CardContent>
        </Card>
      );
    }

    // Log widget access for audit compliance
    logAnalyticsAccess(widgetType, { position });

    switch (widgetType) {
      case 'performance_overview':
        return <EnhancedPerformanceWidget {...commonProps} />;
      case 'engagement_trends':
        return <EngagementTrendsWidget {...commonProps} />;
      case 'attrition_risk':
        return <AttritionRiskWidget {...commonProps} />;
      case 'team_comparison':
        return <TeamComparisonWidget {...commonProps} />;
      case 'recognition_feed':
        return <RecognitionFeedWidget {...commonProps} />;
      case 'team_performance':
        return <TeamPerformanceWidget {...commonProps} />;
      case 'performance_basic':
        return <PerformanceOverviewWidget {...commonProps} />;
      default:
        // Fallback widget for unknown types
        return (
          <Card {...commonProps} sx={{ height: 300 }}>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <Typography variant="h6">Unknown Widget</Typography>
              </Box>
              <Typography color="textSecondary" gutterBottom>
                Widget type: {widgetType}
              </Typography>
              <Box
                display="flex"
                alignItems="center"
                justifyContent="center"
                height={150}
                sx={{
                  bgcolor: 'grey.50',
                  borderRadius: 1,
                  border: '1px dashed',
                  borderColor: 'grey.300'
                }}
              >
                <Typography variant="body2" color="textSecondary">
                  Widget type not recognized
                </Typography>
              </Box>
            </CardContent>
          </Card>
        );
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Dashboard Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" gutterBottom>
            <DashboardIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Analytics Dashboard
          </Typography>
          {selectedDashboard && (
            <Typography variant="subtitle1" color="textSecondary">
              {selectedDashboard.name}
              {selectedDashboard.description && ` - ${selectedDashboard.description}`}
            </Typography>
          )}
        </Box>

        <Box display="flex" gap={1} alignItems="center">
          <Button
            variant="outlined"
            startIcon={<ShareIcon />}
            onClick={() => setExportDialogOpen(true)}
            disabled={!selectedDashboard}
            size="small"
          >
            Export
          </Button>

          {dashboards.map((dashboard) => (
            <Chip
              key={dashboard.id}
              label={dashboard.name}
              variant={selectedDashboard?.id === dashboard.id ? 'filled' : 'outlined'}
              color={selectedDashboard?.id === dashboard.id ? 'primary' : 'default'}
              onClick={() => setSelectedDashboard(dashboard)}
              onDelete={dashboard.isDefault ? undefined : () => handleDeleteDashboard(dashboard)}
              deleteIcon={
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleMenuOpen(e, dashboard);
                  }}
                >
                  <MoreVertIcon fontSize="small" />
                </IconButton>
              }
            />
          ))}
        </Box>
      </Box>

      {/* Dashboard Content */}
      {selectedDashboard ? (
        <Grid container spacing={3}>
          {selectedDashboard.widgetConfig &&
            (selectedDashboard.widgetConfig as any).widgets?.map((widget: any, index: number) => (
              <Grid item xs={12} md={6} lg={4} key={index}>
                {renderWidget(widget.type, widget.position)}
              </Grid>
            ))}

          {/* 🔐 NIS2-COMPLIANT: Enhanced default widgets with real data */}
          {(!selectedDashboard.widgetConfig || !(selectedDashboard.widgetConfig as any).widgets?.length) && (
            <>
              {/* Top Row - Key Performance Indicators */}
              <Grid item xs={12} md={6}>
                {renderWidget('performance_overview', { x: 0, y: 0 })}
              </Grid>
              <Grid item xs={12} md={6}>
                {renderWidget('engagement_trends', { x: 1, y: 0 })}
              </Grid>

              {/* Second Row - Risk Analysis and Team Insights */}
              <Grid item xs={12} md={6}>
                {renderWidget('attrition_risk', { x: 0, y: 1 })}
              </Grid>
              <Grid item xs={12} md={6}>
                {renderWidget('team_comparison', { x: 1, y: 1 })}
              </Grid>

              {/* Third Row - Additional Insights */}
              <Grid item xs={12} md={4}>
                {renderWidget('recognition_feed', { x: 0, y: 2 })}
              </Grid>
              <Grid item xs={12} md={4}>
                {renderWidget('team_performance', { x: 1, y: 2 })}
              </Grid>
              <Grid item xs={12} md={4}>
                {renderWidget('performance_basic', { x: 2, y: 2 })}
              </Grid>
            </>
          )}
        </Grid>
      ) : (
        <Box textAlign="center" py={8}>
          <Typography variant="h6" color="textSecondary" gutterBottom>
            No dashboards available
          </Typography>
          <Typography variant="body2" color="textSecondary" mb={3}>
            Create your first analytics dashboard to get started
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setCreateDialogOpen(true)}
          >
            Create Dashboard
          </Button>
        </Box>
      )}

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="add dashboard"
        sx={{ position: 'fixed', bottom: 16, right: 16 }}
        onClick={() => setCreateDialogOpen(true)}
      >
        <AddIcon />
      </Fab>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleMenuClose}>
          <EditIcon sx={{ mr: 1 }} />
          Edit
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ShareIcon sx={{ mr: 1 }} />
          Share
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <SettingsIcon sx={{ mr: 1 }} />
          Configure
        </MenuItem>
        <MenuItem onClick={() => menuDashboard && handleDeleteDashboard(menuDashboard)}>
          <DeleteIcon sx={{ mr: 1 }} />
          Delete
        </MenuItem>
      </Menu>

      {/* Create Dashboard Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Create New Dashboard</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Dashboard Name"
            fullWidth
            variant="outlined"
            value={newDashboardName}
            onChange={(e) => setNewDashboardName(e.target.value)}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Description (Optional)"
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            value={newDashboardDescription}
            onChange={(e) => setNewDashboardDescription(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleCreateDashboard}
            variant="contained"
            disabled={!newDashboardName.trim()}
          >
            Create
          </Button>
        </DialogActions>
      </Dialog>

      {/* 🔐 NIS2-COMPLIANT: Export Dialog with audit logging */}
      <ExportDialog
        open={exportDialogOpen}
        onClose={() => setExportDialogOpen(false)}
        dashboardData={selectedDashboard}
      />
    </Box>
  );
};

export default AnalyticsDashboard;
